
-- First, let's create a function to seed users in Supabase Auth with metadata
-- This will be used to create all the users with their project names and full names

-- Create or replace the function to create users with metadata
CREATE OR REPLACE FUNCTION create_auth_user_with_metadata(
  user_email TEXT,
  user_password TEXT,
  project_name TEXT,
  full_name TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id UUID;
BEGIN
  -- Create user in auth.users with metadata
  INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_user_meta_data,
    raw_app_meta_data,
    is_super_admin,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
  ) VALUES (
    '00000000-0000-0000-0000-000000000000',
    gen_random_uuid(),
    'authenticated',
    'authenticated',
    user_email,
    crypt(user_password, gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    json_build_object('project_name', project_name, 'full_name', full_name)::jsonb,
    '{}'::jsonb,
    FALSE,
    '',
    '',
    '',
    ''
  )
  RETURNING id INTO new_user_id;
  
  -- Also update the users table with the same information
  INSERT INTO public.users (id, email, name, role)
  VALUES (new_user_id, user_email, full_name, 'caller')
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    name = EXCLUDED.name,
    role = EXCLUDED.role;
    
  RETURN new_user_id;
END;
$$;

-- Now let's seed all the users from your list
SELECT create_auth_user_with_metadata('<EMAIL>', 'Rishitaamp@234', 'Dale Carnegie', 'Rishita Sharma');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Vanditaamp@567', 'Dale Carnegie', 'Vandita Tiwari');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Sanjeevaniamp@789', 'Dale Carnegie', 'Sanjeevani Singhal');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Apoorvaamp@456', 'Dale Carnegie', 'Apoorva Chauhan');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Poojaamp@321', 'DTSS', 'Pooja Rautela');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Nandiniamp@654', 'DTSS', 'Nandini Dabral');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Vanshikaamp@987', 'DTSS', 'Vanshika Thapa');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Shrishtiamp@432', 'DTSS', 'Shrishti Negi');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Jaskiratamp@765', 'DTSS', 'Jaskirat Kaur');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Sanskritiamp@123', 'DTSS', 'Sanskriti Gurung');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Avaniamp@543', 'DTSS', 'Avani Rai');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Akshitaamp@876', 'DTSS', 'Akshita Sati');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Arjooamp@345', 'DTSS', 'Arjoo Rawat');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Subhikshaamp@678', 'DTSS', 'Subhiksha Rawat');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Adarshamp@912', 'Hungerbox', 'Adarsh Kumar');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Anshikaamp@234', 'Hungerbox', 'Anshika Bhandari');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Khushiamp@567', 'Hungerbox', 'Khushi Chauhan');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Sonaliamp@890', 'Hungerbox', 'Sonali Prasad');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Khushiamp@432', 'Hungerbox', 'Khushi Kuthal');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Priyanshiamp@765', 'Hungerbox', 'Priyanshi Bhatt');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Nikitaamp@198', 'Hungerbox', 'Nikita Kohli');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Kirenamp@543', 'Hungerbox', 'Kiren Topwal');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Muskanamp@876', 'Hungerbox', 'Muskan Arora');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Arushiamp@321', 'Hungerbox', 'Arushi Negi');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Muskanamp@654', 'Hungerbox', 'Muskan Singh');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Divyanshiamp@234', 'Hungerbox', 'Divyanshi Gurung');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Shivaniamp@567', 'laiqa/perfect corp', 'Shivani Panwar');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Aartiamp@890', 'Priya Living', 'Aarti Raunchhela');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Prakritiamp@432', 'SIS', 'Prakriti Mondaiyka');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Prachiamp@765', 'SIS', 'Prachi Singh');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Aasthaamp@198', 'SIS', 'Aastha Bhandari');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Simranamp@543', 'SIS 2.0', 'Simran Thapa');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Ayushiamp@876', 'SIS 2.0', 'Ayushi Mahara');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Manojamp@321', 'SIS NAG', 'Manoj Singh Bisht');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Ayushamp@654', 'SIS NAG', 'Ayush Bisht');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Vikasamp@987', 'SIS NAG', 'Vikas Thapa');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Harshikaamp@234', 'SIS NAG', 'Harshika Kunwar');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Kalpanaamp@567', 'SIS NAG', 'Kalpana Verma');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Nehaamp@890', 'SIS NAG', 'Neha Rana');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Aditiamp@432', 'SIS NAG', 'Aditi Tiwari');
SELECT create_auth_user_with_metadata('<EMAIL>', 'Akanshaamp@765', 'UniQ', 'Akansha Sharma');
SELECT create_auth_user_with_metadata('<EMAIL>', 'PrashantADMIN@123', 'ADMIN', 'Prashant Kanyal');

-- Drop the function after seeding (cleanup)
DROP FUNCTION IF EXISTS create_auth_user_with_metadata(TEXT, TEXT, TEXT, TEXT);
