/*
  # Fix prospects table schema

  1. Schema Updates
    - Ensure `id` column exists as primary key with auto-increment
    - Verify all expected columns are present
    - Add any missing columns that the application expects

  2. Data Integrity
    - Preserve existing data during schema updates
    - Ensure proper constraints and indexes

  3. Security
    - Enable RLS on prospects table
    - Add appropriate policies for authenticated users
*/

-- First, check if the table exists and create it if it doesn't
CREATE TABLE IF NOT EXISTS prospects (
  id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  full_name text NOT NULL,
  company_name text NOT NULL,
  prospect_email text,
  prospect_number text,
  prospect_number2 text,
  prospect_number3 text,
  prospect_number4 text,
  prospect_linkedin text,
  prospect_city text
);

-- If the table exists but doesn't have an id column, we need to add it
DO $$
BEGIN
  -- Check if id column exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'prospects' AND column_name = 'id'
  ) THEN
    -- Add id column as primary key
    ALTER TABLE prospects ADD COLUMN id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY;
  END IF;
END $$;

-- Ensure all required columns exist
DO $$
BEGIN
  -- Add missing columns if they don't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'prospects' AND column_name = 'full_name'
  ) THEN
    ALTER TABLE prospects ADD COLUMN full_name text NOT NULL DEFAULT '';
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'prospects' AND column_name = 'company_name'
  ) THEN
    ALTER TABLE prospects ADD COLUMN company_name text NOT NULL DEFAULT '';
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'prospects' AND column_name = 'prospect_email'
  ) THEN
    ALTER TABLE prospects ADD COLUMN prospect_email text;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'prospects' AND column_name = 'prospect_number'
  ) THEN
    ALTER TABLE prospects ADD COLUMN prospect_number text;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'prospects' AND column_name = 'prospect_number2'
  ) THEN
    ALTER TABLE prospects ADD COLUMN prospect_number2 text;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'prospects' AND column_name = 'prospect_number3'
  ) THEN
    ALTER TABLE prospects ADD COLUMN prospect_number3 text;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'prospects' AND column_name = 'prospect_number4'
  ) THEN
    ALTER TABLE prospects ADD COLUMN prospect_number4 text;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'prospects' AND column_name = 'prospect_linkedin'
  ) THEN
    ALTER TABLE prospects ADD COLUMN prospect_linkedin text;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'prospects' AND column_name = 'prospect_city'
  ) THEN
    ALTER TABLE prospects ADD COLUMN prospect_city text;
  END IF;
END $$;

-- Enable Row Level Security
ALTER TABLE prospects ENABLE ROW LEVEL SECURITY;

-- Create policy for authenticated users to read prospects
CREATE POLICY IF NOT EXISTS "Authenticated users can read prospects"
  ON prospects
  FOR SELECT
  TO authenticated
  USING (true);

-- Create policy for authenticated users to insert prospects (if needed)
CREATE POLICY IF NOT EXISTS "Authenticated users can insert prospects"
  ON prospects
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Create policy for authenticated users to update prospects (if needed)
CREATE POLICY IF NOT EXISTS "Authenticated users can update prospects"
  ON prospects
  FOR UPDATE
  TO authenticated
  USING (true);